/*
 * Copyright (c) 2015,robinjim(<EMAIL>)
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.robin.comm.util.word.itext;

public class WordHeaderFooterSection {
	private String headerString;
	private String footerString;
	private String footerpicName;
	private String headerpicname;
	public String getHeaderString() {
		return headerString;
	}
	public void setHeaderString(String headerString) {
		this.headerString = headerString;
	}
	public String getFooterString() {
		return footerString;
	}
	public void setFooterString(String footerString) {
		this.footerString = footerString;
	}
	public String getFooterpicName() {
		return footerpicName;
	}
	public void setFooterpicName(String footerpicName) {
		this.footerpicName = footerpicName;
	}
	public String getHeaderpicname() {
		return headerpicname;
	}
	public void setHeaderpicname(String headerpicname) {
		this.headerpicname = headerpicname;
	}
	
	

}
