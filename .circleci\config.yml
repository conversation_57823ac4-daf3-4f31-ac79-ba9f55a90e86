
version: 2
jobs:
  build:
    docker:
    # specify the version you desire here
    - image: circleci/openjdk:11.0


    working_directory: ~/frame

    environment:
      # Customize the JVM maximum heap limit
      MAVEN_OPTS: -Xmx3200m

    steps:
    - checkout

    # Download and cache dependencies
    - restore_cache:
        keys:
        - v1-dependencies-{{ checksum "pom.xml" }}
        # fallback to using the latest cache if no exact match is found
        - v1-dependencies-

    - run: mvn -q clean package -DskipTests

    - save_cache:
        paths:
        - ~/.m2
        key: v1-dependencies-{{ checksum "pom.xml" }}

    # run tests!
    #- run: mvn -q clean package -DskipTests