package com.robin.comm.util.ppt;

public class PptHeaderFooterSection {
	private String headerStr;
	private String headerPic;
	private String footerStr;
	private String footerPic;
	private String nodeHeaderStr;
	private String nodeFooterStr;
	public String getHeaderStr() {
		return headerStr;
	}
	public void setHeaderStr(String headerStr) {
		this.headerStr = headerStr;
	}
	public String getHeaderPic() {
		return headerPic;
	}
	public void setHeaderPic(String headerPic) {
		this.headerPic = headerPic;
	}
	public String getFooterStr() {
		return footerStr;
	}
	public void setFooterStr(String footerStr) {
		this.footerStr = footerStr;
	}
	public String getFooterPic() {
		return footerPic;
	}
	public void setFooterPic(String footerPic) {
		this.footerPic = footerPic;
	}
	public String getNodeHeaderStr() {
		return nodeHeaderStr;
	}
	public void setNodeHeaderStr(String nodeHeaderStr) {
		this.nodeHeaderStr = nodeHeaderStr;
	}
	public String getNodeFooterStr() {
		return nodeFooterStr;
	}
	public void setNodeFooterStr(String nodeFooterStr) {
		this.nodeFooterStr = nodeFooterStr;
	}

}
