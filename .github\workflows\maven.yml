name: Java CI

on: [push]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - name: <PERSON><PERSON>
      uses: actions/cache@v4
      with:
        path: ~/.m2/repository/org
        key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
        restore-keys: |
            ${{ runner.os }}-maven-
  
    - uses: actions/checkout@v1
    - name: Set up JDK 11
      uses: actions/setup-java@v1
      with:
        java-version: 11
    - name: Build with Maven
      run: mvn -B package -DskipTests --file pom.xml 
