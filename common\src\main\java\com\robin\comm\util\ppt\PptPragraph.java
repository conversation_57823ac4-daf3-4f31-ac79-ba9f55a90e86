package com.robin.comm.util.ppt;

public class PptPragraph {
	private String context;
	private int[] fontColor;
	private int fontSize;
	private int posx;
	private int posy;
	private int height;
	private int width;

	public String getContext() {
		return context;
	}
	public void setContext(String context) {
		this.context = context;
	}
	public int[] getFontColor() {
		return fontColor;
	}
	public void setFontColor(int[] fontColor) {
		this.fontColor = fontColor;
	}
	public int getFontSize() {
		return fontSize;
	}
	public void setFontSize(int fontSize) {
		this.fontSize = fontSize;
	}
	public int getPosx() {
		return posx;
	}
	public void setPosx(int posx) {
		this.posx = posx;
	}
	public int getPosy() {
		return posy;
	}
	public void setPosy(int posy) {
		this.posy = posy;
	}
	public int getHeight() {
		return height;
	}
	public void setHeight(int height) {
		this.height = height;
	}
	public int getWidth() {
		return width;
	}
	public void setWidth(int width) {
		this.width = width;
	}
	

}
